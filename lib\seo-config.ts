/**
 * Configuration SEO centralisée pour Parler Algérien
 * Optimisée pour les réseaux sociaux et le référencement
 */

export const seoConfig = {
  // Informations de base
  siteName: 'Parler <PERSON>',
  siteUrl: 'https://parleralgerien.com',
  defaultTitle: 'Parler <PERSON> - <PERSON> d\'arabe algérien (darija)',
  defaultDescription: 'Rejoignez +500 étudiants qui apprennent l\'algérien avec notre méthode unique. Cours interactifs, enseignants natifs, résultats garantis !',
  
  // Réseaux sociaux
  social: {
    facebook: 'https://www.facebook.com/parleralgerien/',
    twitter: 'https://x.com/parleralgerien',
    instagram: 'https://www.instagram.com/parler_algerien/',
    youtube: 'https://www.youtube.com/@apprendreaparleralgerien',
    tiktok: 'https://www.tiktok.com/@parleralgerien'
  },
  
  // Handles des réseaux sociaux
  socialHandles: {
    twitter: '@parleralgerien',
    facebook: '@parleralgerien',
    instagram: '@parler_algerien',
    youtube: '@apprendreaparleralgerien',
    tiktok: '@parleralgerien'
  },
  
  // Images par défaut
  images: {
    ogImage: '/images/og-image-placeholder.svg',
    logo: '/images/logo6.svg',
    favicon: '/favicon.png'
  },
  
  // Mots-clés principaux
  keywords: [
    'darija algérien',
    'cours de langue',
    'apprendre l\'algérien',
    'dialecte algérien',    
    'cours en ligne',
    'langue arabe',
    'apprentissage langue',
    'formation algérien',
    'école arabe algérien',
    'communauté algérienne',
    'réseaux sociaux algérien',        
  ],
  
  // Configuration Open Graph
  openGraph: {
    type: 'website',
    locale: 'fr_FR',
    siteName: 'Parler Algérien'
  },
  
  // Configuration Twitter
  twitter: {
    cardType: 'summary_large_image',
    site: '@parleralgerien',
    creator: '@parleralgerien'
  },
  
  // Informations de contact
  contact: {
    email: '<EMAIL>',
    supportedLanguages: ['fr', 'ar']
  },
  
  // Informations sur l'organisation
  organization: {
    name: 'Parler Algérien',
    alternateName: 'École d\'arabe algérien (darija)',
    foundingDate: '2020',
    addressCountry: 'FR',
    description: 'École en ligne spécialisée dans l\'apprentissage de l\'algérien (darija) avec des cours interactifs, des exercices pratiques et des enseignants natifs.'
  }
};

/**
 * Génère les métadonnées pour une page spécifique
 */
export function generatePageMetadata(page: {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
}) {
  const title = page.title ? `${page.title} | ${seoConfig.siteName}` : seoConfig.defaultTitle;
  const description = page.description || seoConfig.defaultDescription;
  const image = page.image || seoConfig.images.ogImage;
  const url = page.url ? `${seoConfig.siteUrl}${page.url}` : seoConfig.siteUrl;
  const keywords = page.keywords ? [...seoConfig.keywords, ...page.keywords] : seoConfig.keywords;

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url,
      siteName: seoConfig.siteName,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      locale: seoConfig.openGraph.locale,
      type: seoConfig.openGraph.type,
    },
    twitter: {
      card: seoConfig.twitter.cardType,
      site: seoConfig.twitter.site,
      creator: seoConfig.twitter.creator,
      title,
      description,
      images: [image],
    },
    alternates: {
      canonical: url,
    },
  };
}

/**
 * Génère les données structurées pour les réseaux sociaux
 */
export function generateSocialLinksStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: seoConfig.organization.name,
    url: seoConfig.siteUrl,
    sameAs: Object.values(seoConfig.social),
    contactPoint: {
      '@type': 'ContactPoint',
      email: seoConfig.contact.email,
      contactType: 'customer service',
      availableLanguage: seoConfig.contact.supportedLanguages
    }
  };
}
