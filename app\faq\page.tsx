import { Metadata } from "next";
import dynamic from "next/dynamic";
import { JsonLd, getFaqJsonLd } from "@/components/json-ld";

const FAQSection = dynamic(
  () =>
    import("@/components/faq-section").then((mod) => ({
      default: mod.FAQSection,
    })),
  {
    loading: () => (
      <div className="h-[300px] bg-gray-50 flex items-center justify-center">
        <p>Chargement de la FAQ...</p>
      </div>
    ),
  }
);

export const metadata: Metadata = {
  title: "Questions Fréquentes | Parler Algérien",
  description:
    "Trouvez des réponses aux questions les plus fréquemment posées sur l'apprentissage de l'arabe algérien. Formation, tarifs, méthodes d'enseignement.",
  keywords: [
    "FAQ parler algérien",
    "questions formation",
    "apprentissage algérien",
    "cours en ligne",
    "tarifs formation",
  ],
};

// Données FAQ pour les données structurées
const faqQuestions = [
  {
    question: "Quand est-ce que je pourrais démarrer ma formation ?",
    answer:
      "Vous pouvez démarrer votre formation immédiatement après inscription. Vous recevez automatiquement vos accès par email.",
  },
  {
    question: "Combien coûte la formation ?",
    answer:
      "Nous proposons plusieurs formules adaptées à tous les budgets. Consultez notre page formules pour plus de détails.",
  },
  {
    question: "Les cours sont-ils adaptés aux débutants ?",
    answer:
      "Oui, nos cours sont conçus pour tous les niveaux, du débutant complet au niveau avancé.",
  },
];

export default function FAQPage() {
  return (
    <>
      <JsonLd data={getFaqJsonLd(faqQuestions)} />
      <main className="flex-1">
        <FAQSection />
      </main>
    </>
  );
}
