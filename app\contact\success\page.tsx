"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import dynamic from "next/dynamic"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { Facebook, Instagram, Youtube } from "lucide-react"
import { iconVariants } from "@/components/animation-variants"
const ReactConfetti = dynamic(() => import('react-confetti'), {
  ssr: false
})
const socialIcons = [
  {
    name: "Facebook",
    icon: <Facebook className="h-10 w-10 md:h-12 md:w-12" />,
    color: "bg-blue-600",
    delay: 0,
    link: "https://www.facebook.com/parleralgerien/"
  },
  {
    name: "Instagram",
    icon: <Instagram className="h-10 w-10 md:h-12 md:w-12" />,
    color: "bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500",
    delay: 0.1,
    link: "https://www.instagram.com/parler_algerien/"
  },
  {
    name: "YouTube",
    icon: <Youtube className="h-10 w-10 md:h-12 md:w-12" />,
    color: "bg-red-600",
    delay: 0.2,
    link: "https://www.youtube.com/@apprendreaparleralgerien"
  },
  {
    name: "TikTok",
    icon: <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-10 w-10 md:h-12 md:w-12 text-white"
    >
      <path d="M22.5 9.84202C20.4357 9.84696 18.4221 9.20321 16.7435 8.00171V16.3813C16.7429 17.9333 16.2685 19.4482 15.3838 20.7233C14.499 21.9984 13.246 22.973 11.7923 23.5168C10.3387 24.0606 8.75362 24.1477 7.24914 23.7664C5.74466 23.3851 4.39245 22.5536 3.37333 21.383C2.3542 20.2125 1.71674 18.7587 1.54617 17.2161C1.3756 15.6735 1.68007 14.1156 2.41884 12.7507C3.15762 11.3858 4.2955 10.279 5.68034 9.57823C7.06517 8.87746 8.63095 8.61616 10.1683 8.82927V13.0439C9.4648 12.8227 8.70938 12.8293 8.0099 13.063C7.31041 13.2966 6.70265 13.7453 6.2734 14.345C5.84415 14.9446 5.61536 15.6646 5.6197 16.402C5.62404 17.1395 5.8613 17.8567 6.29759 18.4512C6.73387 19.0458 7.34688 19.4873 8.04906 19.7127C8.75125 19.9381 9.5067 19.9359 10.2075 19.7063C10.9084 19.4768 11.5188 19.0316 11.9515 18.4345C12.3843 17.8374 12.6173 17.1188 12.6173 16.3813V0H16.7435C16.7406 0.348435 16.7698 0.696395 16.8307 1.03948V1.03948C16.9741 1.80537 17.2722 2.53396 17.7068 3.18068C18.1415 3.8274 18.7035 4.37867 19.3585 4.80075C20.2903 5.41688 21.3829 5.74528 22.5 5.74505V9.84202Z" />
    </svg>,
    color: "bg-black",
    delay: 0.3,
    link: "https://www.tiktok.com/@parleralgerien"
  },
];
export default function ContactSuccessPage() {
  const router = useRouter()
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  })

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-background">
      <ReactConfetti
        width={windowSize.width}
        height={windowSize.height}
        recycle={false}
        numberOfPieces={500}
        gravity={0.3}
      />
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6 text-center w-75"
      >
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-10 h-10 text-green-600">
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
        </div>

        <h1 className="text-3xl font-bold tracking-tight">Message envoyé avec succès !</h1>
        
        <p className="text-muted-foreground">
          Merci de nous avoir contacté. Nous vous répondrons dans les plus brefs délais.
        </p>

        <div className="space-y-4">
          <p className="text-muted-foreground">
            Rejoignez-nous sur nos réseaux sociaux pour ne rien manquer de l'actualité !
          </p>
          <div className="flex justify-center gap-6">
            {socialIcons.map((social, index) => (
              <motion.a
                key={social.name}
                href={social.link}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center"
                variants={iconVariants}
                custom={index}
                whileHover="hover"
                transition={{
                  delay: social.delay,
                  type: "spring",
                  stiffness: 300,
                  damping: 10,
                }}
              >
                <div
                  className={`flex items-center justify-center p-5 rounded-full ${social.color} text-white shadow-lg mb-4`}
                >
                  {social.icon}
                </div>
              </motion.a>
            ))}
          </div>
        </div>

        <div className="pt-6">
          <Button 
            onClick={() => router.push('/')}
            className="w-full sm:w-auto"
          >
            Retour à l'accueil
          </Button>
        </div>
      </motion.div>
    </div>
  )
} 