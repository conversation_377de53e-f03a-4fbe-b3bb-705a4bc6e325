"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { LazyMotion, domAnimation, m, AnimatePresence } from "framer-motion"
import { Menu, X } from "lucide-react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  // Fermer le menu lorsque l'écran devient plus grand que md
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMenuOpen(false)
      }
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Détecter le défilement pour ajouter un effet au header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Empêcher le défilement du body lorsque le menu est ouvert
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }
    return () => {
      document.body.style.overflow = ""
    }
  }, [isMenuOpen])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const menuItems = [    
    { href: "/formation", label: "Formation" },
    { href: "/enseignant", label: "Enseignant" },
    { href: "/formules", label: "Formules" },
    { href: "/faq", label: "FAQ" },
  ]

  return (
    <LazyMotion features={domAnimation}>
      {/* Skip Links pour l'accessibilité */}
      <div className="sr-only focus-within:not-sr-only">
        <a
          href="#main-content"
          className="absolute top-0 left-0 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-br-md focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Aller au contenu principal
        </a>
        <a
          href="#navigation"
          className="absolute top-0 left-32 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-br-md focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Aller à la navigation
        </a>
      </div>
      <m.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`sticky top-0 z-40 w-98 lg:w-[70%] items-center justify-between flex h-16 mx-auto my-2 px-6 shadow-sm rounded-lg border backdrop-blur supports-[backdrop-filter]:bg-background/80 ${
          scrolled ? "bg-background/95 shadow-sm" : "bg-background/80"
        }`}
      >
        <Link href="/">
          <m.div
            className="flex items-center gap-2 cursor-pointer"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Image
              src="/images/logo6.svg?height=40&width=40&text=PA"
              alt="Parler Algérien Logo"
              width={200}
              height={80}
            />
          </m.div>
        </Link>

        {/* Menu desktop */}
        <nav id="navigation" className="hidden md:flex gap-6" role="navigation" aria-label="Navigation principale">
          {menuItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="text-sm font-medium hover:text-green-600 transition-colors"
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Actions desktop */}
        <div className="hidden md:flex items-center gap-4 pr-5">
          <m.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button className="btn-primary" asChild>
              <Link
                href="https://etudiant.parleralgerien.com/dashboard/fr/login"
                target="_blank"
                rel="noopener noreferrer"
              >
                Acceder à la formation
              </Link>
            </Button>
          </m.div>
        </div>

        {/* Menu mobile button */}
        <div className="flex md:hidden">
          <Button variant="ghost" size="icon" onClick={toggleMenu} aria-label="Menu">
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </m.header>

      {/* Mobile menu overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <m.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 z-50 md:hidden"
            onClick={closeMenu}
          />
        )}
      </AnimatePresence>

      {/* Menu mobile drawer - style darkrise */}
      <AnimatePresence>
        {isMenuOpen && (
          <m.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className="fixed top-0 left-0 right-0 bg-white z-[60] md:hidden rounded-b-xl shadow-md"
          >
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center">
                <Image
                  src="/images/logo6.svg?height=40&width=40&text=PA"
                  alt="Parler Algérien Logo"
                  width={150}
                  height={40}
                />
              </div>
              <Button variant="ghost" size="icon" onClick={closeMenu} aria-label="Fermer">
                <X className="h-6 w-6" />
              </Button>
            </div>
            <div className="p-4">
              <nav className="flex flex-col">
                {menuItems.map((item) => (
                  <Link
                    key={item.label}
                    href={item.href}
                    className="py-3 text-base font-medium text-gray-800 hover:text-green-600 transition-colors"
                    onClick={closeMenu}
                  >
                    {item.label}
                  </Link>
                ))}
                <Link
                  href="https://etudiant.parleralgerien.com/dashboard/fr/login"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-4 flex items-center justify-center p-3 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
                  onClick={closeMenu}
                >
                  <span className="flex items-center">
                    Acceder à la formation
                    <span className="ml-2">›</span>
                  </span>
                </Link>
              </nav>
            </div>
          </m.div>
        )}
      </AnimatePresence>
    </LazyMotion>
  )
}
