declare namespace NodeJS {
  interface ProcessEnv {
    // Stripe Configuration
    STRIPE_SECRET_KEY: string;
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
    NEXT_PUBLIC_SITE_URL: string;

    // Stripe Products (Legacy)
    STRIPE_FORMULEA: string;
    STRIPE_FORMULEA_PRICE: string;
    STRIPE_FORMULEB: string;
    STRIPE_FORMULEB_PRICE: string;
    STRIPE_FORMULEC: string;
    STRIPE_FORMULEC_PRICE: string;

    // Stripe Webhook
    STRIPE_WEBHOOK_SECRET: string;

    // Stripe Payment Links - Nouvelles Formules
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIER_PAS: string;
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PACK_AVANCE: string;
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_COACHING_MONTHLY: string;
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_COACHING_UNIQUE: string;
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIUM_MONTHLY: string;
    NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIUM_UNIQUE: string;

    // Email Configuration
    RESEND_API_KEY: string;
    RESEND_EMAIL: string;

    // reCAPTCHA Configuration
    RECAPTCHA_SECRET_KEY: string;
    NEXT_PUBLIC_RECAPTCHA_SITE_KEY: string;

    // System.io Configuration
    SYSTEM_IO_API: string;
    SYSTEM_IO_FORMATION_DECOUVERTE_ID: string;
    SYSTEM_IO_FORMATION_COMPLETE_ID: string;
  }
}
