"use client";

import { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { staggerContainer, iconVariants } from "./animation-variants";
import {
  Facebook,
  Instagram,
  Youtube,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useMounted } from "@/hooks/use-mounted";

export function CommunitySection() {
  const sectionRef = useRef(null);
  const buttonRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const isCentered = useInView(buttonRef, { once: false, amount: 0.8 });
  const [isHovered, setIsHovered] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const mounted = useMounted();
  const [counts, setCounts] = useState({
    facebook: 0,
    instagram: 0,
    youtube: 0,
    tiktok: 0,
    total: 0,
  });

  // Configuration des compteurs cibles pour les réseaux sociaux
  const targetCounts = {
    facebook: 500, // Nouvelle page Facebook
    instagram: 11000, // Valeur par défaut, sera mise à jour
    youtube: 23000,
    tiktok: 35000,
    total: 70000,
  };

  // Durée de l'animation du compteur en millisecondes
  const animationDuration = 2000;

  // Détection de l'appareil mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Vérification initiale au chargement
    checkIfMobile();

    // Écouteur d'événement pour le redimensionnement
    window.addEventListener("resize", checkIfMobile);

    // Nettoyage de l'écouteur d'événement
    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, []);

  // Mettre à jour isHovered en fonction de isCentered sur mobile
  useEffect(() => {
    if (isMobile) {
      setIsHovered(isCentered);
    }
  }, [isCentered, isMobile]);

  // Animation du compteur avec easing
  useEffect(() => {
    if (isInView) {
      const startTime = Date.now();

      // Intervalle pour l'animation du compteur
      const interval = setInterval(() => {
        const elapsedTime = Date.now() - startTime;
        const progress = Math.min(elapsedTime / animationDuration, 1);

        // Fonction d'easing pour une animation plus naturelle
        const easeOutQuad = (t: number) => t * (2 - t);
        const easedProgress = easeOutQuad(progress);

        // Mise à jour des compteurs avec l'animation
        setCounts({
          facebook: Math.round(easedProgress * targetCounts.facebook),
          instagram: Math.round(easedProgress * targetCounts.instagram),
          youtube: Math.round(easedProgress * targetCounts.youtube),
          tiktok: Math.round(easedProgress * targetCounts.tiktok),
          total: Math.round(easedProgress * targetCounts.total),
        });

        if (progress >= 1) {
          clearInterval(interval);
        }
      }, 16); // ~60fps pour une animation fluide

      return () => clearInterval(interval);
    }
  }, [isInView]);



  // Configuration des étoiles pour l'animation
  const stars = [
    { size: 6, delay: 0, duration: 1.5 },
    { size: 4, delay: 0.3, duration: 2 },
    { size: 5, delay: 0.7, duration: 1.8 },
  ];

  // Configuration des icônes de réseaux sociaux
  const socialIcons = [
    {
      name: "Facebook",
      icon: <Facebook className="h-12 w-12 md:h-16 md:w-16" />,
      color: "bg-blue-600",
      count: counts.facebook,
      delay: 0,
      link: "https://www.facebook.com/parleralgerien/"
    },
    {
      name: "Instagram",
      icon: <Instagram className="h-12 w-12 md:h-16 md:w-16" />,
      color: "bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500",
      count: counts.instagram,
      delay: 0.1,
      link: "https://www.instagram.com/parler_algerien/"
    },
    {
      name: "YouTube",
      icon: <Youtube className="h-12 w-12 md:h-16 md:w-16" />,
      color: "bg-red-600",
      count: counts.youtube,
      delay: 0.2,
      link: "https://www.youtube.com/@apprendreaparleralgerien"
    },
    {
      name: "TikTok",
      icon: <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="currentColor"
        className="h-12 w-12 md:h-16 md:w-16 text-white"
      >
        <path d="M22.5 9.84202C20.4357 9.84696 18.4221 9.20321 16.7435 8.00171V16.3813C16.7429 17.9333 16.2685 19.4482 15.3838 20.7233C14.499 21.9984 13.246 22.973 11.7923 23.5168C10.3387 24.0606 8.75362 24.1477 7.24914 23.7664C5.74466 23.3851 4.39245 22.5536 3.37333 21.383C2.3542 20.2125 1.71674 18.7587 1.54617 17.2161C1.3756 15.6735 1.68007 14.1156 2.41884 12.7507C3.15762 11.3858 4.2955 10.279 5.68034 9.57823C7.06517 8.87746 8.63095 8.61616 10.1683 8.82927V13.0439C9.4648 12.8227 8.70938 12.8293 8.0099 13.063C7.31041 13.2966 6.70265 13.7453 6.2734 14.345C5.84415 14.9446 5.61536 15.6646 5.6197 16.402C5.62404 17.1395 5.8613 17.8567 6.29759 18.4512C6.73387 19.0458 7.34688 19.4873 8.04906 19.7127C8.75125 19.9381 9.5067 19.9359 10.2075 19.7063C10.9084 19.4768 11.5188 19.0316 11.9515 18.4345C12.3843 17.8374 12.6173 17.1188 12.6173 16.3813V0H16.7435C16.7406 0.348435 16.7698 0.696395 16.8307 1.03948V1.03948C16.9741 1.80537 17.2722 2.53396 17.7068 3.18068C18.1415 3.8274 18.7035 4.37867 19.3585 4.80075C20.2903 5.41688 21.3829 5.74528 22.5 5.74505V9.84202Z" />
      </svg>,
      color: "bg-black",
      count: counts.tiktok,
      delay: 0.3,
      link: "https://www.tiktok.com/@parleralgerien"
    },
  ];

  return (
    <section
      ref={sectionRef}
      className="w-full py-12 md:py-16 lg:py-20 bg-gradient-to-b from-white to-gray-50"
    >
      <div className="container px-4 md:px-6">
        <motion.div
          className="flex flex-col items-center justify-center space-y-4 text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.6 }}
        >
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-blue-100 px-3 py-1 text-sm text-blue-700">
              Communauté
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">
              Rejoignez Notre Communauté
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl">
              Faites partie de notre communauté grandissante et restez connecté
              avec nous.
            </p>
          </div>
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 mb-12"
        >
          {socialIcons.map((social, index) => (
            <motion.div
              key={social.name}
              className="flex flex-col items-center"
              variants={iconVariants}
              custom={index}
              whileHover="hover"
              transition={{
                delay: social.delay,
                type: "spring",
                stiffness: 300,
                damping: 10,
              }}
            >
              <a
                href={social.link}
                target="_blank"
                rel="noopener noreferrer"
                className={`flex items-center justify-center p-6 rounded-full ${social.color} text-white shadow-lg mb-4`}
              >
                {social.icon}
              </a>
              <h3 className="text-xl font-bold">{social.name}</h3>
              <div className="text-2xl font-bold mt-2 text-blue-600">
                {social.count.toLocaleString()}+{" "}
                <span className="text-sm font-normal text-gray-500">
                  abonnés
                </span>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="flex flex-col items-center text-center space-y-8"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={
            isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
          }
          transition={{ duration: 0.8, delay: 0.8 }}
          ref={buttonRef}
        >
          {/* Compteur total */}
          <motion.div
            className="text-white px-10 py-3 rounded-xl shadow-xl relative overflow-hidden"
            initial={{ y: 50 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
            style={{
              position: "relative",
              zIndex: 1,
            }}
          >
            {/* Fond animé */}
            <div
              className="absolute inset-0 z-0"
              style={{
                background:
                  "linear-gradient(-45deg, #4267B2, #FF0000)",
                backgroundSize: "400% 400%",
                animation: "gradient 15s ease infinite",
              }}
            />

            {/* Animation des particules de couleur */}
            <div className="absolute inset-0 z-0 opacity-50">
              {mounted && [...Array(20)].map((_, i) => {
                const icons = [
                  <Instagram key="ig" className="w-6 h-6 text-[black]" />,
                  <Youtube key="yt" className="w-6 h-6 text-[black]" />,
                  <svg
                    key="tk"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="w-6 h-6 text-[black]"
                  >
                    <path d="M22.5 9.84202C20.4357 9.84696 18.4221 9.20321 16.7435 8.00171V16.3813C16.7429 17.9333 16.2685 19.4482 15.3838 20.7233C14.499 21.9984 13.246 22.973 11.7923 23.5168C10.3387 24.0606 8.75362 24.1477 7.24914 23.7664C5.74466 23.3851 4.39245 22.5536 3.37333 21.383C2.3542 20.2125 1.71674 18.7587 1.54617 17.2161C1.3756 15.6735 1.68007 14.1156 2.41884 12.7507C3.15762 11.3858 4.2955 10.279 5.68034 9.57823C7.06517 8.87746 8.63095 8.61616 10.1683 8.82927V13.0439C9.4648 12.8227 8.70938 12.8293 8.0099 13.063C7.31041 13.2966 6.70265 13.7453 6.2734 14.345C5.84415 14.9446 5.61536 15.6646 5.6197 16.402C5.62404 17.1395 5.8613 17.8567 6.29759 18.4512C6.73387 19.0458 7.34688 19.4873 8.04906 19.7127C8.75125 19.9381 9.5067 19.9359 10.2075 19.7063C10.9084 19.4768 11.5188 19.0316 11.9515 18.4345C12.3843 17.8374 12.6173 17.1188 12.6173 16.3813V0H16.7435C16.7406 0.348435 16.7698 0.696395 16.8307 1.03948V1.03948C16.9741 1.80537 17.2722 2.53396 17.7068 3.18068C18.1415 3.8274 18.7035 4.37867 19.3585 4.80075C20.2903 5.41688 21.3829 5.74528 22.5 5.74505V9.84202Z" />
                  </svg>
                ];

                // Valeurs fixes pour éviter les problèmes d'hydratation
                const positions = [
                  { left: 18.6, top: 63.8, opacity: 0.54, icon: 0, duration: 8, delay: 1 },
                  { left: 70.2, top: 18.1, opacity: 0.28, icon: 1, duration: 6, delay: 2 },
                  { left: 45.3, top: 85.4, opacity: 0.41, icon: 2, duration: 7, delay: 0.5 },
                  { left: 12.7, top: 32.9, opacity: 0.35, icon: 0, duration: 9, delay: 3 },
                  { left: 88.1, top: 67.2, opacity: 0.48, icon: 1, duration: 5, delay: 1.5 },
                  { left: 34.5, top: 12.6, opacity: 0.31, icon: 2, duration: 8.5, delay: 4 },
                  { left: 76.8, top: 91.3, opacity: 0.42, icon: 0, duration: 6.5, delay: 0.8 },
                  { left: 23.4, top: 58.7, opacity: 0.37, icon: 1, duration: 7.5, delay: 2.3 },
                  { left: 91.6, top: 25.8, opacity: 0.45, icon: 2, duration: 5.5, delay: 3.7 },
                  { left: 8.9, top: 78.4, opacity: 0.33, icon: 0, duration: 9.5, delay: 1.2 },
                  { left: 67.3, top: 43.1, opacity: 0.39, icon: 1, duration: 6.8, delay: 4.5 },
                  { left: 41.7, top: 7.9, opacity: 0.46, icon: 2, duration: 8.2, delay: 0.3 },
                  { left: 85.2, top: 52.6, opacity: 0.29, icon: 0, duration: 7.8, delay: 2.8 },
                  { left: 29.8, top: 89.5, opacity: 0.43, icon: 1, duration: 5.8, delay: 3.9 },
                  { left: 58.4, top: 36.2, opacity: 0.38, icon: 2, duration: 6.3, delay: 1.7 },
                  { left: 15.1, top: 74.9, opacity: 0.32, icon: 0, duration: 7.2, delay: 0.9 },
                  { left: 82.7, top: 29.4, opacity: 0.47, icon: 1, duration: 6.1, delay: 3.2 },
                  { left: 52.3, top: 81.7, opacity: 0.36, icon: 2, duration: 8.7, delay: 1.8 },
                  { left: 26.9, top: 15.3, opacity: 0.44, icon: 0, duration: 5.9, delay: 4.1 },
                  { left: 73.5, top: 59.8, opacity: 0.34, icon: 1, duration: 7.6, delay: 2.5 }
                ];

                const position = positions[i] || positions[0]; // Fallback au cas où
                const selectedIcon = icons[position.icon];

                return (
                  <motion.div
                    key={i}
                    className="absolute mix-blend-screen"
                    style={{
                      left: `${position.left}%`,
                      top: `${position.top}%`,
                      opacity: position.opacity,
                    }}
                    animate={{
                      y: [0, -20, 0],
                      x: [0, 10, 0],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: position.duration,
                      repeat: Infinity,
                      ease: "linear",
                      delay: position.delay,
                    }}
                  >
                    {selectedIcon}
                  </motion.div>
                );
              })}
            </div>

            {/* Overlay pour assurer la lisibilité du texte */}
            <div className="absolute inset-0 bg-black opacity-30 z-1"></div>

            {/* Contenu */}
            <h3 className="text-2xl md:text-4xl font-bold mb-2 relative z-10">
              {counts.total.toLocaleString()}+ Au total
            </h3>
          </motion.div>

          {/* Bouton de contact avec étoiles */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link href="/formules">
              <Button
                variant="default"
                className="relative group overflow-hidden"
              >
                <span>Vivez une nouvelle aventure</span>

                {/* Étoiles animées */}
                <div className="inline-flex relative ml-1 mr-4">
                  {stars.map((star, index) => (
                    <motion.div
                      key={index}
                      className="absolute"
                      initial={{ opacity: 0.8, scale: 0 }}
                      animate={{
                        opacity: [0.8, 1, 0.8],
                        scale: [0, 1, 0],
                        x: [0, 2, 0],
                        y: [0, -3, 0],
                      }}
                      transition={{
                        duration: star.duration,
                        repeat: Infinity,
                        delay: star.delay,
                        ease: "easeInOut",
                      }}
                      style={{
                        top: index % 2 === 0 ? "-2px" : "-10px",
                        left: `${index * 7}px`,
                        width: `${star.size}px`,
                        height: `${star.size}px`,
                      }}
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="text-yellow-300 w-full h-full"
                      >
                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                      </svg>
                    </motion.div>
                  ))}
                </div>
              </Button>
            </Link>
          </motion.div>
        </motion.div>
      </div>

      {/* Animation keyframes */}
      <style jsx global>{`
        @keyframes gradient {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        @keyframes float {
          0% {
            transform: translateY(0) translateX(0) rotate(0deg);
          }
          33% {
            transform: translateY(-10px) translateX(10px) rotate(120deg);
          }
          66% {
            transform: translateY(10px) translateX(-10px) rotate(240deg);
          }
          100% {
            transform: translateY(0) translateX(0) rotate(360deg);
          }
        }

        @keyframes twinkle {
          0%,
          100% {
            opacity: 0.2;
          }
          50% {
            opacity: 1;
          }
        }
      `}</style>
    </section>
  );
}

// For dynamic import compatibility
export default { CommunitySection };
