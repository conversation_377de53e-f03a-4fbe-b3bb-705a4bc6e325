/**
 * Composant pour les balises meta des réseaux sociaux
 * Optimise le partage sur Facebook, Twitter, etc.
 */

import { seoConfig } from '@/lib/seo-config';

interface SocialMetaTagsProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
}

export function SocialMetaTags({
  title = seoConfig.defaultTitle,
  description = seoConfig.defaultDescription,
  image = seoConfig.images.ogImage,
  url = seoConfig.siteUrl,
  type = 'website'
}: SocialMetaTagsProps) {
  return (
    <>
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={seoConfig.siteName} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={`${seoConfig.siteUrl}${image}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title} />
      <meta property="og:url" content={url} />
      <meta property="og:locale" content="fr_FR" />
      
      {/* Facebook App ID (si vous en avez un) */}
      {/* <meta property="fb:app_id" content="VOTRE_APP_ID" /> */}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={seoConfig.socialHandles.twitter} />
      <meta name="twitter:creator" content={seoConfig.socialHandles.twitter} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={`${seoConfig.siteUrl}${image}`} />
      <meta name="twitter:image:alt" content={title} />
      
      {/* WhatsApp */}
      <meta property="og:image:secure_url" content={`${seoConfig.siteUrl}${image}`} />
      
      {/* Telegram */}
      <meta name="telegram:channel" content={seoConfig.socialHandles.twitter} />
      
      {/* Pinterest */}
      <meta name="pinterest-rich-pin" content="true" />
      
      {/* Balises additionnelles pour le SEO social */}
      <meta name="author" content={seoConfig.siteName} />
      <meta name="publisher" content={seoConfig.siteName} />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      
      {/* Balises pour les moteurs de recherche sociaux */}
      <meta name="theme-color" content="#8dc63f" />
      <meta name="msapplication-TileColor" content="#8dc63f" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={seoConfig.siteName} />
      
      {/* Liens vers les profils sociaux */}
      <link rel="me" href={seoConfig.social.twitter} />
      <link rel="me" href={seoConfig.social.facebook} />
      <link rel="me" href={seoConfig.social.instagram} />
      <link rel="me" href={seoConfig.social.youtube} />
      <link rel="me" href={seoConfig.social.tiktok} />
    </>
  );
}

/**
 * Composant pour les données structurées des réseaux sociaux
 */
export function SocialStructuredData() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: seoConfig.organization.name,
    alternateName: seoConfig.organization.alternateName,
    url: seoConfig.siteUrl,
    logo: `${seoConfig.siteUrl}${seoConfig.images.logo}`,
    description: seoConfig.organization.description,
    foundingDate: seoConfig.organization.foundingDate,
    address: {
      '@type': 'PostalAddress',
      addressCountry: seoConfig.organization.addressCountry
    },
    contactPoint: {
      '@type': 'ContactPoint',
      email: seoConfig.contact.email,
      contactType: 'customer service',
      availableLanguage: seoConfig.contact.supportedLanguages
    },
    sameAs: Object.values(seoConfig.social),
    // Ajout des profils sociaux spécifiques
    socialMediaProfiles: [
      {
        '@type': 'SocialMediaPosting',
        url: seoConfig.social.facebook,
        name: 'Page Facebook officielle'
      },
      {
        '@type': 'SocialMediaPosting', 
        url: seoConfig.social.twitter,
        name: 'Compte Twitter/X officiel'
      },
      {
        '@type': 'SocialMediaPosting',
        url: seoConfig.social.instagram,
        name: 'Compte Instagram officiel'
      },
      {
        '@type': 'SocialMediaPosting',
        url: seoConfig.social.youtube,
        name: 'Chaîne YouTube officielle'
      },
      {
        '@type': 'SocialMediaPosting',
        url: seoConfig.social.tiktok,
        name: 'Compte TikTok officiel'
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
