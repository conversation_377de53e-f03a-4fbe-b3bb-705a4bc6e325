"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { ContactDialog } from "@/components/ui/contact-dialog";
import Image from "next/image";

export function FormulesSection() {
  // Référence et état de visibilité pour les animations
  const formulesRef = useRef(null);
  const formulesInView = useInView(formulesRef, { once: true, amount: 0.3 });

  return (
    <section
      id="formules"
      className="w-full rounded-lg py-3 md:py-6 lg:py-8 bg-muted"
      ref={formulesRef}
    >
      <div className="container px-4 md:px-6">
        {/* Animation du titre et du sous-titre */}

        <div className="flex flex-col items-center justify-center py-12 ">
          {/* Conteneur des animations de blob */}
          <motion.div
            className="relative w-full max-w-lg mx-auto"
            initial={{ opacity: 0 }}
            animate={formulesInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            {/* Animations de blob avec différents délais et couleurs */}
            <div className="absolute top-0 -left-4 w-72 h-72 bg-green-300 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-blob"></div>
            <div className="absolute top-0 -right-4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-blob animation-delay-2000"></div>
            <div className="absolute -bottom-8 left-20 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-blob animation-delay-4000"></div>

            {/* Carte principale avec effet de verre */}
            <motion.div
              className="relative bg-white rounded-2xl shadow-xl p-8 m-4 backdrop-blur-sm bg-opacity-80 border border-gray-200"
              initial={{ scale: 0.8 }}
              animate={formulesInView ? { scale: 1 } : { scale: 0.8 }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 10,
                delay: 0.5,
              }}
            >
              <div className="relative z-10 flex flex-col items-center space-y-6">
                {/* Animation de l'icône */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={
                    formulesInView
                      ? { opacity: 1, y: 0 }
                      : { opacity: 0, y: 20 }
                  }
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-12 h-12 text-green-600"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </motion.div>

                {/* Animation du titre */}
                <motion.h3
                  className="text-2xl font-bold text-center"
                  initial={{ opacity: 0 }}
                  animate={formulesInView ? { opacity: 1 } : { opacity: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                >
                  Merci de nous contacter pour commencer votre aventure
                </motion.h3>

                {/* Animation du texte */}
                <motion.p
                  className="text-center text-muted-foreground max-w-sm"
                  initial={{ opacity: 0 }}
                  animate={formulesInView ? { opacity: 1 } : { opacity: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                >
                  Laissez-nous vos coordonnées et nous prenderons contact avec
                  vous.
                </motion.p>

                {/* Bouton de contact */}
                <div className="w-full flex justify-center">
                  <ContactDialog
                    triggerText="Nous contacter"
                    buttonVariant="default"
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Section des formules regroupées */}
        <motion.div
          className="mx-auto max-w-6xl py-12 relative"
          initial={{ opacity: 0, y: 30 }}
          animate={
            formulesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }
          }
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="bg-gradient-to-br from-white to-green-50 rounded-xl border-2 border-green-200 p-8 shadow-lg relative">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                🚀 Bientôt Disponible
              </span>
            </div>

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-green-700 mb-4">
                Formules d'Apprentissage en Préparation
              </h3>
              <p className="text-muted-foreground">
                Nous développons actuellement plusieurs formules complètes pour
                répondre à tous vos besoins d'apprentissage de l'algérien.
              </p>
            </div>

            {/* Grille des formules */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 relative">
              {/* Effet de transparence global sur tout le conteneur */}
              <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none z-10"></div>
              {/* 1. Premier Pas – Module 1 */}
              <div className="bg-white rounded-xl border-2 border-green-200 transition-all duration-300 relative overflow-hidden">
                {/* Image header */}
                <div className="relative h-32 w-full">
                  <Image
                    src="/images/formules/formule1_head.avif"
                    alt="Header Premier Pas"
                    fill
                    className="object-cover"
                  />
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/40 pointer-events-none"></div>
                </div>

                <div className="p-6">
                  <div className="text-center mb-6">
                    
                    <h3 className="text-xl font-bold text-green-700 mb-2">
                      Premier Pas
                    </h3>
                  </div>

                  {/* Espacement pour maintenir la hauteur */}
                  <div className="h-40"></div>
                </div>

                {/* Effet de fade-out */}
                <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none z-10"></div>
              </div>

              {/* 2. Pack Avancé */}
              <div className="bg-white rounded-xl border-2 border-green-400 transition-all duration-300 relative overflow-hidden">
                {/* Image header */}
                <div className="relative h-32 w-full">
                  <Image
                    src="/images/formules/formule2_head.avif"
                    alt="Header Pack Avancé"
                    fill
                    className="object-cover"
                  />
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/40 pointer-events-none"></div>
                </div>

                <div className="p-6">
                  <div className="text-center mb-6">
                    
                    <h3 className="text-xl font-bold text-green-700 mb-2">
                      Pack Avancé
                    </h3>
                  </div>

                  {/* Espacement pour maintenir la hauteur */}
                  <div className="h-40"></div>
                </div>

                <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none z-10"></div>
              </div>

              {/* 3. Pack Coaching */}
              <div className="bg-white rounded-xl border-2 border-orange-300 transition-all duration-300 relative overflow-hidden">
                {/* Image header */}
                <div className="relative h-32 w-full">
                  <Image
                    src="/images/formules/formule3_head.avif"
                    alt="Header Pack Coaching"
                    fill
                    className="object-cover"
                  />
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/40 pointer-events-none"></div>
                </div>

                <div className="p-6">
                  <div className="text-center mb-6">
                    
                    <h3 className="text-xl font-bold text-green-700 mb-2">
                      Pack Coaching
                    </h3>
                  </div>

                  {/* Espacement pour maintenir la hauteur */}
                  <div className="h-40"></div>
                </div>

                <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none z-10"></div>
              </div>

              {/* 4. Expérience Premium */}
              <div className="bg-white rounded-xl border-2 border-purple-300 transition-all duration-300 relative overflow-hidden">
                {/* Image header */}
                <div className="relative h-32 w-full">
                  <Image
                    src="/images/formules/formule4_head.avif"
                    alt="Header Expérience Premium"
                    fill
                    className="object-cover"
                  />
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/40 pointer-events-none"></div>
                </div>

                <div className="p-6">
                  <div className="text-center mb-6">
                    
                    <h3 className="text-xl font-bold text-green-700 mb-2">
                      Expérience Premium
                    </h3>
                  </div>

                  {/* Espacement pour maintenir la hauteur */}
                  <div className="h-40"></div>
                </div>

                <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none z-10"></div>
              </div>
            </div>

            {/* Note sur les cours supplémentaires */}
            <div className="mt-8 text-center">
              <div className="p-6 bg-white rounded-lg border border-green-200 max-w-4xl mx-auto">
                <h4 className="font-semibold text-green-700 mb-3">
                  Ce qui vous attend dans toutes nos formules :
                </h4>
                <div className="grid gap-2 md:grid-cols-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">
                      Contenus audio, vidéos et textes
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">
                      Exercices et quizz interactifs
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">
                      Progression adaptée à votre rythme
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">
                      Méthode d'apprentissage éprouvée
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">Support pédagogique complet</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm">
                      Accès depuis tous vos appareils
                    </span>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground">
                    <strong>Option supplémentaire :</strong> Possibilité
                    d'ajouter des cours individuels en visioconférence à tout
                    moment pour un accompagnement personnalisé.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Styles globaux pour les animations de blob */}
      <style jsx global>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </section>
  );
}
