"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { toast } from "@/components/ui/use-toast";
import Image from "next/image";

// Types pour les données des formules
interface FormuleFeature {
  text: string;
}

interface FormuleBonus {
  text: string;
  bgColor: string;
  textColor: string;
}

interface FormuleData {
  id: string;
  title: string;
  icon: string;
  iconBgColor: string;
  borderColor: string;
  headerImage: string;
  iconImage: string;
  isPopular?: boolean;
  hasToggle?: boolean;
  pricing: {
    monthly?: string;
    unique?: string;
    total?: string;
  };
  description: string;
  features: FormuleFeature[];
  bonus: FormuleBonus;
  buttonText: string;
  buttonVariant: "outline" | "default" | "secondary";
  paymentLinkKeys: {
    monthly?: string;
    unique?: string;
    single?: string;
  };
}

// Données des formules
const formulesData: FormuleData[] = [
  {
    id: "premier-pas",
    title: "Premier Pas",
    icon: "🌱",
    iconBgColor: "bg-green-100",
    borderColor: "border-green-200",
    headerImage: "/images/formules/formule1_head.avif",
    iconImage: "/images/formules/formule1_icon.png",
    pricing: {
      monthly: "15€",
      total: "15€",
    },
    description: "Découvrez l'algérien avec notre premier module complet",
    features: [
      { text: "Accès au 1er module (15 chapitres)" },
      { text: "Contenus audio, vidéos, exercices" },
    ],
    bonus: {
      text: "💡 Bonus : Possibilité de passer à une formule supérieure à tout moment.",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
    },
    buttonText: "Choisir cette offre",
    buttonVariant: "outline",
    paymentLinkKeys: { single: "premierPas" },
  },
  {
    id: "pack-avance",
    title: "Pack Avancé",
    icon: "🎯",
    iconBgColor: "bg-green-200",
    borderColor: "border-green-400",
    headerImage: "/images/formules/formule2_head.avif",
    iconImage: "/images/formules/formule2_icon.png",
    hasToggle: true,
    pricing: {
      monthly: "19,99€",
      unique: "169,99€",
      total: "179,91€",
    },
    description: "Formation complète en 9 mois avec contenu progressif",
    features: [
      { text: "Accès à 1 module par mois pendant 9 mois" },
      { text: "Contenus audio, vidéos, exercices" },
    ],
    bonus: {
      text: "💡 Bonus : Coaching groupé disponible (15€/séance, max 8)",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
    },
    buttonText: "Choisir cette offre",
    buttonVariant: "default",
    paymentLinkKeys: { monthly: "AvanceMonthly", unique: "AvanceUnique" },
  },
  {
    id: "pack-coaching",
    title: "Pack Coaching",
    icon: "👨‍🏫",
    iconBgColor: "bg-orange-100",
    borderColor: "border-orange-300",
    headerImage: "/images/formules/formule3_head.avif",
    iconImage: "/images/formules/formule3_icon.png",
    hasToggle: true,
    isPopular: true,
    pricing: {
      monthly: "29,99€",
      unique: "249,99€",
      total: "299,90€",
    },
    description: "Formation complète avec coaching groupé inclus",
    features: [
      { text: "1 module par mois + accès progressif" },
      { text: "8 séances de coaching groupé incluses" },
      { text: "Accompagnement personnalisé" },
      { text: "Mise en pratique guidée" },
    ],
    bonus: {
      text: "🎯 Bonus : ≈13€/séance de coaching (au lieu de 15€)",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
    },
    buttonText: "Choisir cette offre",
    buttonVariant: "secondary",
    paymentLinkKeys: { monthly: "coachingMonthly", unique: "coachingUnique" },
  },
  {
    id: "experience-premium",
    title: "Expérience Premium – Suivi hebdo",
    icon: "⭐",
    iconBgColor: "bg-purple-100",
    borderColor: "border-purple-500",
    headerImage: "/images/formules/formule4_head.avif",
    iconImage: "/images/formules/formule4_icon.png",
    hasToggle: true,
    pricing: {
      monthly: "49,99€",
      unique: "439,99€",
      total: "499,90€",
    },
    description: "L'expérience la plus complète avec suivi personnalisé",
    features: [
      { text: "Accès immédiat à tous les modules" },
      { text: "Coaching privé hebdomadaire" },
      { text: "Suivi personnalisé complet" },
    ],
    bonus: {
      text: "⭐ Bonus Premium : Mini-dictionnaire exclusif, Ressources IA avancées, Support dédié prioritaire",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
    },
    buttonText: "Choisir cette offre",
    buttonVariant: "secondary",
    paymentLinkKeys: { monthly: "premiumMonthly", unique: "premiumUnique" },
  },
];

export function FormulesSection() {
  const formulesRef = useRef(null);
  const formulesInView = useInView(formulesRef, { once: true, amount: 0.3 });

  // États pour basculer entre paiement mensuel et unique
  const [isCoachingMonthly, setIsCoachingMonthly] = useState(true);
  const [isPremiumMonthly, setIsPremiumMonthly] = useState(true);
  const [isAvanceMonthly, setIsAvanceMonthly] = useState(true);

  // Payment Links depuis les variables d'environnement
  const paymentLinks = {
    premierPas: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIER_PAS,
    AvanceMonthly: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_AVANCE_MONTHLY,
    AvanceUnique: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_AVANCE_UNIQUE,
    coachingMonthly:
      process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_COACHING_MONTHLY,
    coachingUnique: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_COACHING_UNIQUE,
    premiumMonthly: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIUM_MONTHLY,
    premiumUnique: process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_PREMIUM_UNIQUE,
  };

  // Animations
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0
    },
  };

  // Fonction utilitaire pour obtenir le prix affiché
  const getDisplayPrice = (formule: FormuleData, isMonthly: boolean) => {
    if (!formule.hasToggle)
      return formule.pricing.monthly || formule.pricing.total;
    return isMonthly ? formule.pricing.monthly : formule.pricing.unique;
  };

  // Fonction utilitaire pour obtenir le total affiché
  const getDisplayTotal = (formule: FormuleData, isMonthly: boolean) => {
    if (!formule.hasToggle) return null;
    return isMonthly ? `Total: ${formule.pricing.total}` : "Paiement unique";
  };

  // Fonction utilitaire pour obtenir le lien de paiement
  const getPaymentLink = (formule: FormuleData, isMonthly: boolean) => {
    if (!formule.hasToggle && formule.paymentLinkKeys.single) {
      return paymentLinks[
        formule.paymentLinkKeys.single as keyof typeof paymentLinks
      ];
    }

    const linkKey = isMonthly
      ? formule.paymentLinkKeys.monthly
      : formule.paymentLinkKeys.unique;
    return paymentLinks[linkKey as keyof typeof paymentLinks];
  };

  // Fonction pour gérer la redirection de paiement
  const handlePaymentRedirect = (
    paymentLink: string | undefined,
    formuleName: string
  ) => {
    if (!paymentLink) {
      toast({
        title: "Erreur",
        description: `Le lien de paiement pour la ${formuleName} n'est pas configuré.`,
        variant: "destructive",
      });
      return;
    }
    window.open(paymentLink, "_blank");
  };

  // Fonction pour gérer le toggle de paiement
  const handleToggle = (formuleId: string, isMonthly: boolean) => {
    if (formuleId === "pack-coaching") {
      setIsCoachingMonthly(isMonthly);
    } else if (formuleId === "experience-premium") {
      setIsPremiumMonthly(isMonthly);
    } else if (formuleId === "pack-avance") {
      setIsAvanceMonthly(isMonthly);
    }
  };

  // Fonction pour obtenir l'état du toggle
  const getToggleState = (formuleId: string) => {
    if (formuleId === "pack-coaching") return isCoachingMonthly;
    if (formuleId === "experience-premium") return isPremiumMonthly;
    if (formuleId === "pack-avance") return isAvanceMonthly;
    return true;
  };

  return (
    <section
      className="py-16 bg-gradient-to-br from-green-50 to-blue-50"
      ref={formulesRef}
    >
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={
            formulesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }
          }
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl font-bold tracking-tighter md:text-4xl mb-4">
            Choisissez votre formule d'apprentissage
          </h2>
          <p className="max-w-[900px] mx-auto text-muted-foreground md:text-xl">
            Découvrez nos différentes options adaptées à tous les niveaux et
            objectifs d'apprentissage de l'algérien.
          </p>
        </motion.div>

        <motion.div
          className="mx-auto grid max-w-6xl gap-6 py-12 lg:grid-cols-4"
          variants={staggerContainer}
          initial="hidden"
          animate={formulesInView ? "visible" : "hidden"}
        >
          {formulesData.map((formule) => {
            const isMonthly = getToggleState(formule.id);
            const displayPrice = getDisplayPrice(formule, isMonthly);
            const displayTotal = getDisplayTotal(formule, isMonthly);
            const paymentLink = getPaymentLink(formule, isMonthly);

            return (
              <motion.div
                key={formule.id}
                variants={cardVariants}
                whileHover={{ y: -10, transition: { duration: 0.3 } }}
                className={
                  formule.isPopular ? "lg:scale-105 lg:-translate-y-2" : ""
                }
              >
                <Card
                  className={`h-full transition-all duration-300 ${formule.borderColor} ${formule.isPopular ? "shadow-lg" : ""} relative overflow-hidden`}
                >
                  {formule.isPopular && (
                    <div className="absolute top-2 right-2 z-30">
                      <motion.div
                        className="inline-block rounded-full bg-green-500 px-3 py-1 text-xs text-white font-semibold shadow-lg"
                        animate={{ scale: [1, 1.05, 1] }}
                        transition={{
                          duration: 1.5,
                          repeat: Number.POSITIVE_INFINITY,
                          repeatType: "reverse",
                        }}
                      >
                        Populaire
                      </motion.div>
                    </div>
                  )}

                  {/* Image header */}
                  <div className="relative h-32 w-full group">
                    <Image
                      src={formule.headerImage}
                      alt={`Header ${formule.title}`}
                      fill
                      className="object-cover transition-all duration-300 group-hover:scale-105 group-hover:brightness-75"
                    />
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/40 pointer-events-none"></div>
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 pointer-events-none"></div>
                  </div>

                  <CardHeader>
                    <CardTitle className="text-center">
                      {formule.title}
                    </CardTitle>

                    {formule.hasToggle && (
                      <div className="flex items-center justify-center mb-3">
                        <div className="flex bg-gray-100 rounded-lg p-1">
                          <button
                            type="button"
                            onClick={() => handleToggle(formule.id, true)}
                            className={`px-3 py-1 rounded-md text-sm transition-all ${
                              isMonthly
                                ? "bg-white shadow-sm text-gray-900"
                                : "text-gray-600 hover:text-gray-900"
                            }`}
                          >
                            Mensuel
                          </button>
                          <button
                            type="button"
                            onClick={() => handleToggle(formule.id, false)}
                            className={`px-3 py-1 rounded-md text-sm transition-all ${
                              !isMonthly
                                ? "bg-white shadow-sm text-gray-900"
                                : "text-gray-600 hover:text-gray-900"
                            }`}
                          >
                            Une fois
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold">{displayPrice}</span>
                    </div>
                    {displayTotal && (
                      <div className="text-sm text-muted-foreground font-medium">
                        {displayTotal}
                      </div>
                    )}
                    <CardDescription>{formule.description}</CardDescription>
                  </CardHeader>

                  <CardContent>
                    <ul className="space-y-2 text-sm">
                      {formule.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          {feature.text}
                        </li>
                      ))}
                    </ul>
                    <div
                      className={`mt-4 p-3 ${formule.bonus.bgColor} rounded-lg`}
                    >
                      <p
                        className={`text-sm ${formule.bonus.textColor} font-medium`}
                      >
                        {formule.bonus.text}
                      </p>
                    </div>
                  </CardContent>

                  <CardFooter>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full"
                    >
                      <Button
                        className={`w-full ${formule.id === "experience-premium" ? "bg-purple-600 hover:bg-purple-700" : ""}`}
                        variant={formule.buttonVariant}
                        onClick={() =>
                          handlePaymentRedirect(paymentLink, formule.title)
                        }
                      >
                        {formule.buttonText}
                      </Button>
                    </motion.div>
                  </CardFooter>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={
            formulesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
          }
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <p className="text-sm text-muted-foreground">
            <strong>Cours supplémentaires :</strong> Vous pouvez à tout moment
            demander des cours individuels supplémentaires en visioconférence à
            150€ pour 5 heures de cours.
          </p>
        </motion.div>
      </div>
    </section>
  );
}
