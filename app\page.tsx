import { Metadata } from "next"
import { HeroSection } from "@/components/hero-section"
import { FeaturesGrid } from "@/components/features-grid"
import dynamic from 'next/dynamic'

export const metadata: Metadata = {
  title: "Parler <PERSON> - École d'arabe algérien (darija) #1 au monde",
  description:
    "Apprenez l'algérien (darija) rapidement avec notre méthode unique. +500 étudiants satisfaits, cours interactifs, enseignants natifs. Essai gratuit disponible !",
  keywords: [
    "cours de langue",
    "apprendre l'algérien",
    "dialecte algérien",
    "cours en ligne",
    "langue algérienne",
    "apprentissage langue",
    "formation algérien",
    "école arabe algérien",
    "communauté algérienne",
    "réseaux sociaux algérien",
    "darija algérien",
  ],
  openGraph: {
    title: "École d'arabe algérien (darija) #1 - <PERSON><PERSON><PERSON>",
    description:
      "Rejoignez +500 étudiants qui apprennent l'algérien avec notre méthode unique. Cours interactifs, enseignants natifs, résultats garantis !",
    type: "website",
    url: "https://parleralgerien.com",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Parler Algérien - École d'algérien en ligne avec +500 étudiants satisfaits",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "École d'arabe algérien (darija) #1 - Parler Algérien",
    description:
      "Apprenez l'algérien rapidement avec +500 étudiants satisfaits. Méthode unique, enseignants natifs !",
    images: ["/og-image.png"],
  },
  alternates: {
    canonical: "https://parleralgerien.com",
  },
};

// Importation dynamique des composants lourds
const TestimonialsCarousel = dynamic(() => import("@/components/testimonials-carousel").then(mod => ({ default: mod.TestimonialsCarousel })), {
  loading: () => <div className="h-[400px] flex items-center justify-center bg-gray-50"><p>Chargement des témoignages...</p></div>
})
const VideoTestimonialsSection = dynamic(() => import("@/components/video-testimonials-section").then(mod => ({ default: mod.VideoTestimonialsSection })), {
  loading: () => <div className="h-[300px] bg-gray-50"></div>
})
const CommunitySection = dynamic(() => import("@/components/community-section").then(mod => ({ default: mod.CommunitySection })), {
  loading: () => <div className="h-[200px] bg-gray-50"></div>
})
// Composant client pour gérer les feux d'artifice
const FireworksWrapper = dynamic(() => import("@/components/FireworksWrapper"));

export default function LandingPage() {
  return (
    <main id="main-content" className="flex-1">
      <HeroSection />
      <FeaturesGrid />
      <VideoTestimonialsSection />
      <CommunitySection />
      <TestimonialsCarousel />
      <FireworksWrapper />
    </main>
  )
}